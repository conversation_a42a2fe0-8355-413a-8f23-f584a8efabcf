import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Terminal } from "lucide-react";

interface BootSequenceProps {
  onComplete: () => void;
}

export function BootSequence({ onComplete }: BootSequenceProps) {
  const [stage, setStage] = useState(-1); // -1 means not started
  const [text, setText] = useState('Click to Start System');

  const startBootSequence = async () => {
    const audio = new Audio('/login.wav');
    await audio.play();

    const bootSequence = [
      { text: 'BOOTING RETRO FORUM SYSTEM v1.0...', delay: 1500 },
      { text: 'INITIALIZING MEMORY BANKS...', delay: 1500 },
      { text: 'MEMORY CHECK COMPLETE', delay: 1000 },
      { text: 'LOADING SYSTEM RESOURCES...', delay: 1500 },
      { text: 'ESTABLISHING NETWORK PROTOCOLS...', delay: 1500 },
      { text: 'CONFIGURING DISPLAY PARAMETERS...', delay: 1500 },
      { text: 'CALIBRATING CRT DISPLAY...', delay: 1000 },
      { text: 'INITIALIZING USER INTERFACE...', delay: 1000 },
      { text: 'SYSTEM READY', delay: 1000 },
      { text: 'LAUNCHING RETRO FORUM...', delay: 500 }
    ];

    for (let i = 0; i < bootSequence.length; i++) {
      await new Promise(resolve => {
        setTimeout(() => {
          setStage(i);
          setText(bootSequence[i].text);
          resolve(null);
        }, bootSequence[i].delay);
      });
    }

    // Final delay before completing
    await new Promise(resolve => setTimeout(resolve, 500));
    sessionStorage.setItem('system_booted', 'true'); // Changed from localStorage to sessionStorage
    onComplete();
  };

  if (stage === -1) {
    return (
      <div className="fixed inset-0 bg-black flex flex-col items-center justify-center gap-6">
        <Terminal className="h-16 w-16 text-text" />
        <div className="text-text text-xl mb-4">RETRO FORUM SYSTEM v1.0</div>
        <Button 
          onClick={startBootSequence}
          variant="outline" 
          className="terminal-input hover:text-glow hover:border-text"
        >
          START SYSTEM
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black flex items-center justify-center">
      <div className="font-mono text-text max-w-md w-full p-8">
        <div className="mb-8">
          <div className="h-4 bg-text animate-pulse mb-2"></div>
          <div 
            className="h-4 bg-text animate-pulse" 
            style={{width: `${((stage + 1) * 10)}%`}}
          ></div>
        </div>
        <div className="text-xl">
          <span className="animate-pulse">{text}</span>
          <span className="animate-blink">_</span>
        </div>
      </div>
    </div>
  );
}