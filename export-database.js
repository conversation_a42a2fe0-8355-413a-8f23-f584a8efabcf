
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * RetroForum Database Export Script
 * Automatically exports your database with multiple backup formats
 */

// Configuration
const DB_CONFIG = {
  host: 'localhost',
  user: 'postgres',
  database: 'retro_forum_db',
  // Password will be prompted or use PGPASSWORD env var
};

const BACKUP_DIR = './database-backups';
const DATE = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

// Create backup directory
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Helper function to run shell commands
function runCommand(command, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔄 ${description}...`);
    console.log(`📝 Command: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.warn(`⚠️  Warning: ${stderr}`);
      }
      console.log(`✅ ${description} completed`);
      if (stdout) console.log(stdout);
      resolve();
    });
  });
}

// Main export function
async function exportDatabase() {
  console.log('🚀 RetroForum Database Export Starting...');
  console.log(`📅 Date: ${DATE}`);
  console.log(`📁 Backup Directory: ${BACKUP_DIR}`);
  
  try {
    // 1. Full database dump (SQL format)
    await runCommand(
      `pg_dump -h ${DB_CONFIG.host} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -f "${BACKUP_DIR}/retroforum_full_${DATE}.sql"`,
      'Creating full SQL backup'
    );

    // 2. Custom format dump (compressed, faster restore)
    await runCommand(
      `pg_dump -h ${DB_CONFIG.host} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -Fc -f "${BACKUP_DIR}/retroforum_custom_${DATE}.dump"`,
      'Creating custom format backup'
    );

    // 3. Schema-only dump
    await runCommand(
      `pg_dump -h ${DB_CONFIG.host} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} --schema-only -f "${BACKUP_DIR}/retroforum_schema_${DATE}.sql"`,
      'Creating schema-only backup'
    );

    // 4. Data-only dump
    await runCommand(
      `pg_dump -h ${DB_CONFIG.host} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} --data-only -f "${BACKUP_DIR}/retroforum_data_${DATE}.sql"`,
      'Creating data-only backup'
    );

    // 5. Core tables only (users, threads, messages)
    await runCommand(
      `pg_dump -h ${DB_CONFIG.host} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -t users -t threads -t replies -t messages -t votes -f "${BACKUP_DIR}/retroforum_core_${DATE}.sql"`,
      'Creating core tables backup'
    );

    // 6. Create restore script
    const restoreScript = `#!/bin/bash
# RetroForum Database Restore Script
# Generated: ${new Date().toISOString()}

echo "🔄 RetroForum Database Restore"
echo "Choose restore method:"
echo "1. Full restore (SQL)"
echo "2. Custom format restore"
echo "3. Schema only"
echo "4. Data only"
echo "5. Core tables only"

read -p "Enter choice (1-5): " choice

case $choice in
  1)
    echo "Restoring full SQL backup..."
    psql -h localhost -U postgres -d retro_forum_db -f "retroforum_full_${DATE}.sql"
    ;;
  2)
    echo "Restoring custom format backup..."
    pg_restore -h localhost -U postgres -d retro_forum_db "retroforum_custom_${DATE}.dump"
    ;;
  3)
    echo "Restoring schema only..."
    psql -h localhost -U postgres -d retro_forum_db -f "retroforum_schema_${DATE}.sql"
    ;;
  4)
    echo "Restoring data only..."
    psql -h localhost -U postgres -d retro_forum_db -f "retroforum_data_${DATE}.sql"
    ;;
  5)
    echo "Restoring core tables..."
    psql -h localhost -U postgres -d retro_forum_db -f "retroforum_core_${DATE}.sql"
    ;;
  *)
    echo "Invalid choice"
    exit 1
    ;;
esac

echo "✅ Restore completed!"
`;

    fs.writeFileSync(path.join(BACKUP_DIR, `restore_${DATE}.sh`), restoreScript);
    console.log(`📜 Restore script created: restore_${DATE}.sh`);

    // 7. Create backup manifest
    const manifest = {
      timestamp: new Date().toISOString(),
      database: DB_CONFIG.database,
      backups: {
        full_sql: `retroforum_full_${DATE}.sql`,
        custom_dump: `retroforum_custom_${DATE}.dump`,
        schema_only: `retroforum_schema_${DATE}.sql`,
        data_only: `retroforum_data_${DATE}.sql`,
        core_tables: `retroforum_core_${DATE}.sql`,
        restore_script: `restore_${DATE}.sh`
      },
      tables_included: [
        'users', 'threads', 'replies', 'votes', 'messages',
        'ai_chats', 'ai_chat_sessions', 'chat_room_messages', 'user_events'
      ],
      notes: 'Complete RetroForum database backup including all user data, encryption keys, and configuration'
    };

    fs.writeFileSync(
      path.join(BACKUP_DIR, `manifest_${DATE}.json`),
      JSON.stringify(manifest, null, 2)
    );

    console.log('\n🎉 Database export completed successfully!');
    console.log('\n📊 Backup Summary:');
    console.log(`   📁 Location: ${BACKUP_DIR}/`);
    console.log(`   📄 Full SQL: retroforum_full_${DATE}.sql`);
    console.log(`   📦 Custom: retroforum_custom_${DATE}.dump`);
    console.log(`   🗂️  Schema: retroforum_schema_${DATE}.sql`);
    console.log(`   📋 Data: retroforum_data_${DATE}.sql`);
    console.log(`   🔧 Core: retroforum_core_${DATE}.sql`);
    console.log(`   ⚙️  Restore: restore_${DATE}.sh`);
    console.log(`   📋 Manifest: manifest_${DATE}.json`);

    // Check file sizes
    const files = fs.readdirSync(BACKUP_DIR);
    const backupFiles = files.filter(f => f.includes(DATE));
    
    console.log('\n📏 File Sizes:');
    backupFiles.forEach(file => {
      const filePath = path.join(BACKUP_DIR, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`   ${file}: ${sizeKB} KB`);
    });

  } catch (error) {
    console.error('\n❌ Export failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure PostgreSQL is running');
    console.log('2. Check database credentials');
    console.log('3. Verify database name exists');
    console.log('4. Set PGPASSWORD environment variable if needed');
    process.exit(1);
  }
}

// Run the export
exportDatabase();
