2025-03-03T21:11:10.567Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "sessionId": 4,
  "userId": 1,
  "validationTime": 19
}
2025-03-03T21:11:10.568Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:11:10.569Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "userId": 1,
  "sessionId": 4
}
2025-03-03T21:11:10.569Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:11:10.572Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036270572_cdawltmn",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 4,
  "attachmentsCount": 0
}
2025-03-03T21:11:10.573Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036270572_cdawltmn",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:11:10.918Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036270572_cdawltmn",
  "duration": 345,
  "status": 200,
  "hasData": true
}
2025-03-03T21:11:16.464Z - DEBUG [stream-first-chunk]: First chunk received after 5544ms
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "ttfb": 5544
}
2025-03-03T21:11:18.315Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "messageLength": 4,
  "responseLength": 8953
}
2025-03-03T21:11:18.366Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036270548_0shybytv",
  "processTime": 7798
}
2025-03-03T21:11:27.918Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "sessionId": 4,
  "userId": 1,
  "validationTime": 17
}
2025-03-03T21:11:27.919Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "messageCount": 3,
  "lastUserMessagePreview": "wich deepseek model is this?",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:11:27.919Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "userId": 1,
  "sessionId": 4
}
2025-03-03T21:11:27.919Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:11:27.919Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036287919_8gh3lkx9",
  "messageCount": 4,
  "lastUserMessage": "wich deepseek model is this?...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 4,
  "attachmentsCount": 0
}
2025-03-03T21:11:27.920Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036287919_8gh3lkx9",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:11:28.453Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036287919_8gh3lkx9",
  "duration": 533,
  "status": 200,
  "hasData": true
}
2025-03-03T21:11:38.211Z - DEBUG [stream-first-chunk]: First chunk received after 9757ms
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "ttfb": 9757
}
2025-03-03T21:11:40.803Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "messageLength": 28,
  "responseLength": 15879
}
2025-03-03T21:11:40.872Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036287901_uisk8vbj",
  "processTime": 12953
}
2025-03-03T21:12:16.749Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "sessionId": 5,
  "userId": 1,
  "validationTime": 17
}
2025-03-03T21:12:16.750Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "messageCount": 1,
  "lastUserMessagePreview": "wich deep seek model is this",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:12:16.750Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "userId": 1,
  "sessionId": 5
}
2025-03-03T21:12:16.750Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 5,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:12:16.751Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036336751_pxkn942c",
  "messageCount": 2,
  "lastUserMessage": "wich deep seek model is this...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 5,
  "attachmentsCount": 0
}
2025-03-03T21:12:16.751Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036336751_pxkn942c",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:12:17.098Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036336751_pxkn942c",
  "duration": 347,
  "status": 200,
  "hasData": true
}
2025-03-03T21:12:26.488Z - DEBUG [stream-first-chunk]: First chunk received after 9388ms
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "ttfb": 9388
}
2025-03-03T21:12:28.453Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "userId": 1,
  "sessionId": 5,
  "isDeepThinking": false,
  "messageLength": 28,
  "responseLength": 11121
}
2025-03-03T21:12:28.489Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036336732_lek38snm",
  "processTime": 11739
}
2025-03-03T21:12:46.465Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "sessionId": 5,
  "userId": 1,
  "validationTime": 17
}
2025-03-03T21:12:46.465Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "messageCount": 3,
  "lastUserMessagePreview": "this is not R1?",
  "processingTime": 0,
  "isDeepThinking": true
}
2025-03-03T21:12:46.465Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "userId": 1,
  "sessionId": 5
}
2025-03-03T21:12:46.466Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 5,
  "isDeepThinking": true,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:12:46.466Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036366466_srlhs0e1",
  "messageCount": 4,
  "lastUserMessage": "this is not R1?...",
  "stream": true,
  "isDeepThinking": true,
  "userId": 1,
  "sessionId": 5,
  "attachmentsCount": 0
}
2025-03-03T21:12:46.466Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036366466_srlhs0e1",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:12:46.883Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036366466_srlhs0e1",
  "duration": 417,
  "status": 200,
  "hasData": true
}
2025-03-03T21:12:55.548Z - DEBUG [stream-first-chunk]: First chunk received after 8664ms
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "ttfb": 8664
}
2025-03-03T21:12:59.621Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "chunkCount": 50,
  "contentLength": 23113,
  "elapsedTime": 12737
}
2025-03-03T21:13:00.900Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "userId": 1,
  "sessionId": 5,
  "isDeepThinking": true,
  "messageLength": 15,
  "responseLength": 31828
}
2025-03-03T21:13:00.964Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036366448_drxcregs",
  "processTime": 14499
}
2025-03-03T21:18:29.105Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 5,
  "userId": 1,
  "messageCount": 2
}
2025-03-03T21:18:37.012Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "sessionId": 6,
  "userId": 1,
  "validationTime": 23
}
2025-03-03T21:18:37.012Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:18:37.013Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "userId": 1,
  "sessionId": 6
}
2025-03-03T21:18:37.013Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:18:37.017Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036717017_57ata8bj",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 6,
  "attachmentsCount": 0
}
2025-03-03T21:18:37.018Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036717017_57ata8bj",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:18:37.460Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036717017_57ata8bj",
  "duration": 442,
  "status": 200,
  "hasData": true
}
2025-03-03T21:18:44.029Z - DEBUG [stream-first-chunk]: First chunk received after 6568ms
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "ttfb": 6568
}
2025-03-03T21:18:45.859Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "messageLength": 4,
  "responseLength": 10018
}
2025-03-03T21:18:45.904Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036716989_q2ijsd52",
  "processTime": 8892
}
2025-03-03T21:18:57.797Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "sessionId": 6,
  "userId": 1,
  "validationTime": 18
}
2025-03-03T21:18:57.797Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "messageCount": 3,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:18:57.798Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "userId": 1,
  "sessionId": 6
}
2025-03-03T21:18:57.798Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:18:57.798Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036737798_fiawua1w",
  "messageCount": 4,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 6,
  "attachmentsCount": 0
}
2025-03-03T21:18:57.799Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036737798_fiawua1w",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:18:58.129Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036737798_fiawua1w",
  "duration": 330,
  "status": 200,
  "hasData": true
}
2025-03-03T21:19:03.809Z - DEBUG [stream-first-chunk]: First chunk received after 5679ms
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "ttfb": 5679
}
2025-03-03T21:19:05.292Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "messageLength": 33,
  "responseLength": 7628
}
2025-03-03T21:19:05.332Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036737779_jzn1c08k",
  "processTime": 7535
}
2025-03-03T21:19:39.290Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "sessionId": 6,
  "userId": 1,
  "validationTime": 20
}
2025-03-03T21:19:39.291Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "messageCount": 5,
  "lastUserMessagePreview": "no im asking if this is a deepseek ai model",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:19:39.291Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "userId": 1,
  "sessionId": 6
}
2025-03-03T21:19:39.291Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:19:39.291Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036779291_ym0hkxo0",
  "messageCount": 6,
  "lastUserMessage": "no im asking if this is a deepseek ai model...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 6,
  "attachmentsCount": 0
}
2025-03-03T21:19:39.292Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036779291_ym0hkxo0",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:19:39.588Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036779291_ym0hkxo0",
  "duration": 296,
  "status": 200,
  "hasData": true
}
2025-03-03T21:19:48.028Z - DEBUG [stream-first-chunk]: First chunk received after 8439ms
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "ttfb": 8439
}
2025-03-03T21:19:50.199Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "messageLength": 43,
  "responseLength": 12905
}
2025-03-03T21:19:50.279Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036779270_nc2wf9j8",
  "processTime": 10988
}
2025-03-03T21:20:02.567Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "sessionId": 6,
  "userId": 1,
  "validationTime": 19
}
2025-03-03T21:20:02.567Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "messageCount": 7,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:20:02.567Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "userId": 1,
  "sessionId": 6
}
2025-03-03T21:20:02.567Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:20:02.570Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036802570_1mx2bkuw",
  "messageCount": 8,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 6,
  "attachmentsCount": 0
}
2025-03-03T21:20:02.570Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036802570_1mx2bkuw",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:20:02.967Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036802570_1mx2bkuw",
  "duration": 397,
  "status": 200,
  "hasData": true
}
2025-03-03T21:20:10.893Z - DEBUG [stream-first-chunk]: First chunk received after 7925ms
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "ttfb": 7925
}
2025-03-03T21:20:12.861Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": false,
  "messageLength": 4,
  "responseLength": 10289
}
2025-03-03T21:20:12.899Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036802548_0fi04bnm",
  "processTime": 10332
}
2025-03-03T21:20:17.881Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "sessionId": 6,
  "userId": 1,
  "validationTime": 18
}
2025-03-03T21:20:17.881Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "messageCount": 9,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": true
}
2025-03-03T21:20:17.881Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "userId": 1,
  "sessionId": 6
}
2025-03-03T21:20:17.881Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": true,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:20:17.882Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036817882_ydmkke16",
  "messageCount": 10,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": true,
  "userId": 1,
  "sessionId": 6,
  "attachmentsCount": 0
}
2025-03-03T21:20:17.882Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036817882_ydmkke16",
  "endpoint": "/chat/completions",
  "model": "deepseek-reasoner",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:20:18.218Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036817882_ydmkke16",
  "duration": 336,
  "status": 200,
  "hasData": true
}
2025-03-03T21:20:25.547Z - DEBUG [stream-first-chunk]: First chunk received after 7328ms
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "ttfb": 7328
}
2025-03-03T21:20:29.449Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "chunkCount": 50,
  "contentLength": 28283,
  "elapsedTime": 11230
}
2025-03-03T21:20:32.831Z - DEBUG [stream-progress]: Stream progress: 100 chunks received
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "chunkCount": 100,
  "contentLength": 56260,
  "elapsedTime": 14612
}
2025-03-03T21:20:35.028Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "userId": 1,
  "sessionId": 6,
  "isDeepThinking": true,
  "messageLength": 33,
  "responseLength": 74415
}
2025-03-03T21:20:35.129Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036817863_jg1cd798",
  "processTime": 17248
}
2025-03-03T21:20:52.454Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 4,
  "userId": 1
}
2025-03-03T21:20:54.483Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 6,
  "userId": 1,
  "messageCount": 5
}
2025-03-03T21:20:55.943Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 5,
  "userId": 1,
  "messageCount": 2
}
2025-03-03T21:20:56.644Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 6,
  "userId": 1,
  "messageCount": 5
}
2025-03-03T21:21:03.333Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "sessionId": 7,
  "userId": 1,
  "validationTime": 19
}
2025-03-03T21:21:03.333Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "messageCount": 1,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": true
}
2025-03-03T21:21:03.333Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "userId": 1,
  "sessionId": 7
}
2025-03-03T21:21:03.333Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:21:03.333Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036863333_rhjlty68",
  "messageCount": 2,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": true,
  "userId": 1,
  "sessionId": 7,
  "attachmentsCount": 0
}
2025-03-03T21:21:03.334Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036863333_rhjlty68",
  "endpoint": "/chat/completions",
  "model": "deepseek-reasoner",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:21:03.693Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036863333_rhjlty68",
  "duration": 359,
  "status": 200,
  "hasData": true
}
2025-03-03T21:21:06.719Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 5,
  "userId": 1,
  "messageCount": 2
}
2025-03-03T21:21:06.742Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 5,
  "userId": 1
}
2025-03-03T21:21:08.896Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 6,
  "userId": 1,
  "messageCount": 5
}
2025-03-03T21:21:08.907Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 6,
  "userId": 1
}
2025-03-03T21:21:09.673Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:09.913Z - DEBUG [stream-first-chunk]: First chunk received after 6219ms
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "ttfb": 6219
}
2025-03-03T21:21:10.732Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:13.007Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:13.514Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 50,
  "contentLength": 27967,
  "elapsedTime": 9820
}
2025-03-03T21:21:13.666Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:13.948Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:14.184Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:14.382Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:14.579Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:14.775Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:14.970Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:15.165Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:15.362Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:15.564Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:15.764Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 0
}
2025-03-03T21:21:16.860Z - DEBUG [stream-progress]: Stream progress: 100 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 100,
  "contentLength": 55097,
  "elapsedTime": 13166
}
2025-03-03T21:21:19.729Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "sessionId": 7,
  "userId": 1,
  "validationTime": 18
}
2025-03-03T21:21:19.729Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "messageCount": 1,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": true
}
2025-03-03T21:21:19.730Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "userId": 1,
  "sessionId": 7
}
2025-03-03T21:21:19.730Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:21:19.730Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036879730_9z5rnwqx",
  "messageCount": 2,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": true,
  "userId": 1,
  "sessionId": 7,
  "attachmentsCount": 0
}
2025-03-03T21:21:19.731Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036879730_9z5rnwqx",
  "endpoint": "/chat/completions",
  "model": "deepseek-reasoner",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:21:20.056Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036879730_9z5rnwqx",
  "duration": 326,
  "status": 200,
  "hasData": true
}
2025-03-03T21:21:20.240Z - DEBUG [stream-progress]: Stream progress: 150 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 150,
  "contentLength": 80764,
  "elapsedTime": 16546
}
2025-03-03T21:21:23.738Z - DEBUG [stream-progress]: Stream progress: 200 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 200,
  "contentLength": 108797,
  "elapsedTime": 20044
}
2025-03-03T21:21:27.604Z - DEBUG [stream-progress]: Stream progress: 250 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 250,
  "contentLength": 135825,
  "elapsedTime": 23910
}
2025-03-03T21:21:30.952Z - DEBUG [stream-progress]: Stream progress: 300 chunks received
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "chunkCount": 300,
  "contentLength": 162362,
  "elapsedTime": 27258
}
2025-03-03T21:21:32.073Z - DEBUG [stream-first-chunk]: First chunk received after 12016ms
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "ttfb": 12016
}
2025-03-03T21:21:32.395Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "messageLength": 33,
  "responseLength": 173514
}
2025-03-03T21:21:32.553Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036863314_ulwbt198",
  "processTime": 29220
}
2025-03-03T21:21:34.608Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "sessionId": 7,
  "userId": 1,
  "validationTime": 18
}
2025-03-03T21:21:34.609Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "messageCount": 3,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": true
}
2025-03-03T21:21:34.609Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "userId": 1,
  "sessionId": 7
}
2025-03-03T21:21:34.609Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:21:34.609Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741036894609_785kqut4",
  "messageCount": 4,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": true,
  "userId": 1,
  "sessionId": 7,
  "attachmentsCount": 0
}
2025-03-03T21:21:34.610Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741036894609_785kqut4",
  "endpoint": "/chat/completions",
  "model": "deepseek-reasoner",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:21:34.897Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741036894609_785kqut4",
  "duration": 287,
  "status": 200,
  "hasData": true
}
2025-03-03T21:21:39.507Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 50,
  "contentLength": 27096,
  "elapsedTime": 19450
}
2025-03-03T21:21:42.878Z - DEBUG [stream-progress]: Stream progress: 100 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 100,
  "contentLength": 53322,
  "elapsedTime": 22821
}
2025-03-03T21:21:46.271Z - DEBUG [stream-progress]: Stream progress: 150 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 150,
  "contentLength": 79268,
  "elapsedTime": 26214
}
2025-03-03T21:21:46.895Z - DEBUG [stream-first-chunk]: First chunk received after 11997ms
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "ttfb": 11997
}
2025-03-03T21:21:49.658Z - DEBUG [stream-progress]: Stream progress: 200 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 200,
  "contentLength": 106692,
  "elapsedTime": 29601
}
2025-03-03T21:21:53.029Z - DEBUG [stream-progress]: Stream progress: 250 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 250,
  "contentLength": 132628,
  "elapsedTime": 32972
}
2025-03-03T21:21:55.409Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 50,
  "contentLength": 25654,
  "elapsedTime": 20511
}
2025-03-03T21:21:56.384Z - DEBUG [stream-progress]: Stream progress: 300 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 300,
  "contentLength": 160005,
  "elapsedTime": 36327
}
2025-03-03T21:21:58.793Z - DEBUG [stream-progress]: Stream progress: 100 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 100,
  "contentLength": 52758,
  "elapsedTime": 23895
}
2025-03-03T21:21:59.829Z - DEBUG [stream-progress]: Stream progress: 350 chunks received
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "chunkCount": 350,
  "contentLength": 186565,
  "elapsedTime": 39772
}
2025-03-03T21:22:01.460Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "messageLength": 33,
  "responseLength": 198623
}
2025-03-03T21:22:01.611Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036879711_v4tnan4b",
  "processTime": 41882
}
2025-03-03T21:22:02.185Z - DEBUG [stream-progress]: Stream progress: 150 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 150,
  "contentLength": 78723,
  "elapsedTime": 27287
}
2025-03-03T21:22:05.593Z - DEBUG [stream-progress]: Stream progress: 200 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 200,
  "contentLength": 103193,
  "elapsedTime": 30695
}
2025-03-03T21:22:08.997Z - DEBUG [stream-progress]: Stream progress: 250 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 250,
  "contentLength": 130337,
  "elapsedTime": 34099
}
2025-03-03T21:22:12.552Z - DEBUG [stream-progress]: Stream progress: 300 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 300,
  "contentLength": 156933,
  "elapsedTime": 37654
}
2025-03-03T21:22:15.869Z - DEBUG [stream-progress]: Stream progress: 350 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 350,
  "contentLength": 183542,
  "elapsedTime": 40971
}
2025-03-03T21:22:19.205Z - DEBUG [stream-progress]: Stream progress: 400 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 400,
  "contentLength": 210071,
  "elapsedTime": 44307
}
2025-03-03T21:22:22.541Z - DEBUG [stream-progress]: Stream progress: 450 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 450,
  "contentLength": 237142,
  "elapsedTime": 47643
}
2025-03-03T21:22:25.885Z - DEBUG [stream-progress]: Stream progress: 500 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 500,
  "contentLength": 261943,
  "elapsedTime": 50987
}
2025-03-03T21:22:29.226Z - DEBUG [stream-progress]: Stream progress: 550 chunks received
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "chunkCount": 550,
  "contentLength": 289070,
  "elapsedTime": 54328
}
2025-03-03T21:22:32.359Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "userId": 1,
  "sessionId": 7,
  "isDeepThinking": true,
  "messageLength": 33,
  "responseLength": 312593
}
2025-03-03T21:22:32.630Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741036894590_688g43i2",
  "processTime": 58021
}
2025-03-03T21:36:34.725Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 3
}
2025-03-03T21:36:45.569Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "sessionId": 8,
  "userId": 1,
  "validationTime": 19
}
2025-03-03T21:36:45.569Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "messageCount": 1,
  "lastUserMessagePreview": "hello.",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:36:45.570Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-03-03T21:36:45.570Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:36:45.572Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741037805572_hallind5",
  "messageCount": 2,
  "lastUserMessage": "hello....",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 8,
  "attachmentsCount": 0
}
2025-03-03T21:36:45.572Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741037805572_hallind5",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:36:46.312Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741037805572_hallind5",
  "duration": 740,
  "status": 200,
  "hasData": true
}
2025-03-03T21:36:52.852Z - DEBUG [stream-first-chunk]: First chunk received after 7282ms
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "ttfb": 7282,
  "contentLength": 546,
  "isDeepThinking": false
}
2025-03-03T21:36:54.536Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "messageLength": 6,
  "responseLength": 9213
}
2025-03-03T21:36:54.584Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741037805550_o9olon8q",
  "processTime": 9015
}
2025-03-03T21:37:03.907Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "sessionId": 8,
  "userId": 1,
  "validationTime": 19
}
2025-03-03T21:37:03.908Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "messageCount": 3,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:37:03.908Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "messageCount": 3
}
2025-03-03T21:37:03.908Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:37:03.908Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741037823908_zcjy6f10",
  "messageCount": 4,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 8,
  "attachmentsCount": 0
}
2025-03-03T21:37:03.909Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741037823908_zcjy6f10",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:37:04.271Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741037823908_zcjy6f10",
  "duration": 362,
  "status": 200,
  "hasData": true
}
2025-03-03T21:37:12.049Z - DEBUG [stream-first-chunk]: First chunk received after 8141ms
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "ttfb": 8141,
  "contentLength": 542,
  "isDeepThinking": false
}
2025-03-03T21:37:14.220Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "userId": 1,
  "sessionId": 8,
  "isDeepThinking": false,
  "messageLength": 33,
  "responseLength": 12392
}
2025-03-03T21:37:14.274Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741037823888_qpxskotx",
  "processTime": 10366
}
2025-03-03T21:37:22.158Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "sessionId": 9,
  "userId": 1,
  "validationTime": 16
}
2025-03-03T21:37:22.159Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "messageCount": 1,
  "lastUserMessagePreview": "wich version of deepseek is this?",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-03T21:37:22.159Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "userId": 1,
  "sessionId": 9,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-03-03T21:37:22.159Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 9,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-03T21:37:22.160Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741037842160_ca6dsbrw",
  "messageCount": 2,
  "lastUserMessage": "wich version of deepseek is this?...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 9,
  "attachmentsCount": 0
}
2025-03-03T21:37:22.160Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741037842160_ca6dsbrw",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-03T21:37:22.509Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741037842160_ca6dsbrw",
  "duration": 349,
  "status": 200,
  "hasData": true
}
2025-03-03T21:37:28.331Z - DEBUG [stream-first-chunk]: First chunk received after 6172ms
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "ttfb": 6172,
  "contentLength": 543,
  "isDeepThinking": false
}
2025-03-03T21:37:32.227Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "chunkCount": 50,
  "contentLength": 22327,
  "elapsedTime": 10068,
  "chunkRate": "4.97 chunks/sec",
  "responseSize": "21.8 KB",
  "isDeepThinking": false
}
2025-03-03T21:37:33.557Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "userId": 1,
  "sessionId": 9,
  "isDeepThinking": false,
  "messageLength": 33,
  "responseLength": 32102
}
2025-03-03T21:37:33.625Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741037842142_lxitubgv",
  "processTime": 11466
}
2025-03-03T21:37:40.314Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 9,
  "userId": 1
}
2025-03-03T21:37:40.530Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 8,
  "userId": 1,
  "messageCount": 2
}
2025-03-03T21:37:43.359Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 8,
  "userId": 1,
  "messageCount": 2
}
2025-03-03T21:37:43.403Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 8,
  "userId": 1
}
2025-03-03T21:37:43.693Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 3
}
2025-03-03T21:37:44.922Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 7,
  "userId": 1
}
2025-03-03T21:37:44.943Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 7,
  "userId": 1,
  "messageCount": 3
}
2025-03-06T20:59:54.772Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 10,
  "userId": 1,
  "messageCount": 0
}
2025-03-06T21:00:06.871Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 24
}
2025-03-06T21:00:06.871Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-03-06T21:00:06.872Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-03-06T21:00:06.872Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-03-06T21:00:06.874Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1741294806874_sjvig3rv",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-03-06T21:00:06.874Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1741294806874_sjvig3rv",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-03-06T21:00:08.119Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1741294806874_sjvig3rv",
  "duration": 1245,
  "status": 200,
  "hasData": true
}
2025-03-06T21:00:13.456Z - DEBUG [stream-first-chunk]: First chunk received after 6584ms
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "ttfb": 6584,
  "contentLength": 546,
  "isDeepThinking": false
}
2025-03-06T21:00:18.721Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageLength": 4,
  "responseLength": 10550
}
2025-03-06T21:00:18.787Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1741294806847_x6fi27cd",
  "processTime": 11916
}
2025-05-12T22:59:11.715Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-12T23:29:18.539Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-23T04:14:09.434Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-23T04:14:14.002Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1747973653981_xmwjbjcq",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 21
}
2025-05-23T04:14:14.002Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1747973653981_xmwjbjcq",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-05-23T04:14:14.003Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1747973653981_xmwjbjcq",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-05-23T04:14:14.003Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-05-23T04:14:14.003Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-05-23T04:14:14.005Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1747973654005_v1l1hlom",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-05-23T04:14:14.006Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1747973654005_v1l1hlom",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-05-23T04:14:15.863Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1747973655842_xa3a23if",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 21
}
2025-05-23T04:14:15.864Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1747973655842_xa3a23if",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-05-23T04:14:15.864Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1747973655842_xa3a23if",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-05-23T04:14:15.865Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-05-23T04:14:15.865Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-05-23T04:14:15.865Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1747973655865_nll26io8",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-05-23T04:14:15.866Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1747973655865_nll26io8",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-05-23T04:14:18.358Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1747973658337_34cwpkxi",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 21
}
2025-05-23T04:14:18.358Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1747973658337_34cwpkxi",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-05-23T04:14:18.358Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1747973658337_34cwpkxi",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-05-23T04:14:18.359Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-05-23T04:14:18.359Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-05-23T04:14:18.359Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1747973658359_821697c7",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-05-23T04:14:18.359Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1747973658359_821697c7",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-05-24T00:48:55.420Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T01:12:04.177Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T01:12:42.113Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T03:23:02.146Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T03:30:09.908Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T17:57:08.572Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-05-24T17:57:28.108Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-06-10T07:02:47.965Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-06-24T04:09:47.458Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-06-24T04:09:51.168Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1750738190985_goqigrn9",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 183
}
2025-06-24T04:09:51.168Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1750738190985_goqigrn9",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-06-24T04:09:51.169Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1750738190985_goqigrn9",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-06-24T04:09:51.169Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-06-24T04:09:51.170Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-06-24T04:09:51.172Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1750738191172_t42xq9od",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-06-24T04:09:51.177Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1750738191172_t42xq9od",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-06-24T04:09:52.607Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1750738192590_fayf44zu",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 17
}
2025-06-24T04:09:52.607Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1750738192590_fayf44zu",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-06-24T04:09:52.608Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1750738192590_fayf44zu",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-06-24T04:09:52.608Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-06-24T04:09:52.608Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-06-24T04:09:52.608Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1750738192608_iw2wx85s",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-06-24T04:09:52.609Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1750738192608_iw2wx85s",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-06-24T04:09:55.020Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1750738195003_nk56ezaj",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 17
}
2025-06-24T04:09:55.021Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1750738195003_nk56ezaj",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-06-24T04:09:55.021Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1750738195003_nk56ezaj",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-06-24T04:09:55.021Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-06-24T04:09:55.021Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-06-24T04:09:55.021Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1750738195021_wpftur6e",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-06-24T04:09:55.022Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1750738195021_wpftur6e",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-06-24T04:10:09.967Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-06-24T04:10:10.648Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-06-24T04:10:27.775Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-06-24T05:04:32.495Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-06-24T05:18:20.124Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-07-04T06:01:49.424Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1751608909414_uxbsv5cp",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-07-04T06:01:49.426Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1751608909414_uxbsv5cp",
  "userId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-07-04T06:01:49.427Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 4,
  "sessionId": null,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-07-04T06:01:49.427Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-07-04T06:01:49.430Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1751608909430_gzy6jk24",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 4,
  "sessionId": null,
  "attachmentsCount": 0
}
2025-07-04T06:01:49.439Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1751608909430_gzy6jk24",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-07-04T06:01:51.395Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1751608911395_6qqqj83j",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-07-04T06:01:51.396Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1751608911395_6qqqj83j",
  "userId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-07-04T06:01:51.396Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 4,
  "sessionId": null,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-07-04T06:01:51.396Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-07-04T06:01:51.396Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1751608911396_xotxdgu9",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 4,
  "sessionId": null,
  "attachmentsCount": 0
}
2025-07-04T06:01:51.397Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1751608911396_xotxdgu9",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-07-04T06:01:54.300Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1751608914300_332ipyfy",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-07-04T06:01:54.301Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1751608914300_332ipyfy",
  "userId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-07-04T06:01:54.302Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 4,
  "sessionId": null,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true
}
2025-07-04T06:01:54.302Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-07-04T06:01:54.302Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1751608914302_tvyqlscp",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 4,
  "sessionId": null,
  "attachmentsCount": 0
}
2025-07-04T06:01:54.302Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1751608914302_tvyqlscp",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-07-04T06:01:55.257Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 15,
  "userId": 4,
  "messageCount": 0
}
2025-07-04T06:14:56.409Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 16,
  "userId": 4,
  "messageCount": 0
}
2025-07-24T05:07:23.320Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-07-24T05:08:11.151Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-07-24T05:13:19.667Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-06T05:36:09.292Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-06T05:41:05.162Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-14T04:04:03.531Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 16,
  "userId": 4,
  "messageCount": 0
}
2025-08-14T04:21:10.672Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-14T04:21:22.056Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-14T04:27:10.004Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-14T04:48:24.485Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-14T04:48:28.470Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:01:49.637Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:01:53.420Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:06:46.790Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:10:30.439Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:13:57.857Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:20:35.129Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:22:16.426Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:32:09.411Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:32:26.432Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-08-16T21:32:27.843Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:32:31.036Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-08-16T21:34:36.888Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380076869_jdg7dhgz",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 19
}
2025-08-16T21:34:36.888Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380076869_jdg7dhgz",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:34:36.888Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380076869_jdg7dhgz",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-08-16T21:34:36.889Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "deepseek",
  "model": "deepseek-chat"
}
2025-08-16T21:34:36.889Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:34:36.891Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380076891_mhzljryo",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-08-16T21:34:36.896Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380076891_mhzljryo",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:34:38.489Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380078468_cswcyw84",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 21
}
2025-08-16T21:34:38.489Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380078468_cswcyw84",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:34:38.490Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380078468_cswcyw84",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-08-16T21:34:38.490Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "deepseek",
  "model": "deepseek-chat"
}
2025-08-16T21:34:38.490Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:34:38.491Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380078491_vtguld2k",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-08-16T21:34:38.491Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380078491_vtguld2k",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:34:40.915Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380080899_n0ypqpae",
  "sessionId": 11,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T21:34:40.915Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380080899_n0ypqpae",
  "messageCount": 2,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:34:40.916Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380080899_n0ypqpae",
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-08-16T21:34:40.916Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 11,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "deepseek",
  "model": "deepseek-chat"
}
2025-08-16T21:34:40.916Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:34:40.917Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380080917_qp2mfa52",
  "messageCount": 3,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 11,
  "attachmentsCount": 0
}
2025-08-16T21:34:40.917Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380080917_qp2mfa52",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:38:26.862Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:38:33.130Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 12,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:38:40.169Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 12,
  "userId": 1
}
2025-08-16T21:38:40.330Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 11,
  "userId": 1,
  "messageCount": 1
}
2025-08-16T21:38:41.562Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 11,
  "userId": 1
}
2025-08-16T21:38:41.730Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 10,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:38:42.866Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 10,
  "userId": 1
}
2025-08-16T21:38:58.915Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "sessionId": 17,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T21:38:58.916Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "messageCount": 1,
  "lastUserMessagePreview": "test5",
  "processingTime": 1,
  "isDeepThinking": false
}
2025-08-16T21:38:58.916Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:38:58.917Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "deepseek",
  "model": "deepseek-chat"
}
2025-08-16T21:38:58.917Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:38:58.919Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380338919_fsv8srxy",
  "messageCount": 2,
  "lastUserMessage": "test5...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 17,
  "attachmentsCount": 0
}
2025-08-16T21:38:58.920Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380338919_fsv8srxy",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:38:59.496Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1755380338919_fsv8srxy",
  "duration": 576,
  "status": 200,
  "hasData": true
}
2025-08-16T21:39:01.537Z - DEBUG [stream-first-chunk]: First chunk received after 2621ms
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "ttfb": 2621,
  "contentLength": 567,
  "isDeepThinking": false
}
2025-08-16T21:39:04.696Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "messageLength": 5,
  "responseLength": 20420
}
2025-08-16T21:39:04.766Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1755380338897_ffjgs8lh",
  "processTime": 5851
}
2025-08-16T21:39:28.149Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 1
}
2025-08-16T21:43:35.551Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "sessionId": 17,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T21:43:35.552Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "messageCount": 2,
  "lastUserMessagePreview": "tell me a story",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:43:35.552Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "messageCount": 2
}
2025-08-16T21:43:35.552Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "deepseek",
  "model": "deepseek-chat"
}
2025-08-16T21:43:35.553Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:43:35.553Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380615553_z78nttll",
  "messageCount": 3,
  "lastUserMessage": "tell me a story...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 17,
  "attachmentsCount": 0
}
2025-08-16T21:43:35.556Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380615553_z78nttll",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:43:35.822Z - DEBUG [api-response]: API request successful
Data: {
  "requestId": "req_1755380615553_z78nttll",
  "duration": 266,
  "status": 200,
  "hasData": true
}
2025-08-16T21:43:37.915Z - DEBUG [stream-first-chunk]: First chunk received after 2363ms
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "ttfb": 2363,
  "contentLength": 574,
  "isDeepThinking": false
}
2025-08-16T21:43:42.084Z - DEBUG [stream-progress]: Stream progress: 50 chunks received
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "chunkCount": 50,
  "contentLength": 24694,
  "elapsedTime": 6532,
  "chunkRate": "7.65 chunks/sec",
  "responseSize": "24.12 KB",
  "isDeepThinking": false
}
2025-08-16T21:43:45.751Z - DEBUG [stream-progress]: Stream progress: 100 chunks received
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "chunkCount": 100,
  "contentLength": 50786,
  "elapsedTime": 10199,
  "chunkRate": "9.8 chunks/sec",
  "responseSize": "49.6 KB",
  "isDeepThinking": false
}
2025-08-16T21:43:49.419Z - DEBUG [stream-progress]: Stream progress: 150 chunks received
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "chunkCount": 150,
  "contentLength": 75196,
  "elapsedTime": 13867,
  "chunkRate": "10.82 chunks/sec",
  "responseSize": "73.43 KB",
  "isDeepThinking": false
}
2025-08-16T21:43:53.087Z - DEBUG [stream-progress]: Stream progress: 200 chunks received
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "chunkCount": 200,
  "contentLength": 98788,
  "elapsedTime": 17535,
  "chunkRate": "11.41 chunks/sec",
  "responseSize": "96.47 KB",
  "isDeepThinking": false
}
2025-08-16T21:43:53.310Z - DEBUG [persistence]: Persisting chat history to database
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "userId": 1,
  "sessionId": 17,
  "isDeepThinking": false,
  "messageLength": 15,
  "responseLength": 100356
}
2025-08-16T21:43:53.423Z - DEBUG [persistence-complete]: Chat history persisted successfully
Data: {
  "requestId": "req_1755380615536_vfo8r1d3",
  "processTime": 17872
}
2025-08-16T21:44:54.411Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380694391_0yrsus1d",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 20
}
2025-08-16T21:44:54.411Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380694391_0yrsus1d",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:44:54.412Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380694391_0yrsus1d",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:44:54.412Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:44:54.412Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:44:54.413Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380694413_zt4icp43",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:44:54.413Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380694413_zt4icp43",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:44:56.317Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380696267_joekzi0h",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 50
}
2025-08-16T21:44:56.317Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380696267_joekzi0h",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:44:56.317Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380696267_joekzi0h",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:44:56.318Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:44:56.328Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:44:56.329Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380696329_76o3k4xs",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:44:56.335Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380696329_76o3k4xs",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:44:58.775Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380698758_yxsezvvc",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T21:44:58.776Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380698758_yxsezvvc",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:44:58.776Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380698758_yxsezvvc",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:44:58.776Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:44:58.777Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:44:58.777Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380698777_p8crv0x2",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:44:58.781Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380698777_p8crv0x2",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:48:06.821Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:48:30.904Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:49:21.151Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380961133_igz2cp10",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T21:49:21.151Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380961133_igz2cp10",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:49:21.151Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380961133_igz2cp10",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:49:21.151Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:49:21.151Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:49:21.152Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380961152_4tgakj0d",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:49:21.152Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380961152_4tgakj0d",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:49:22.577Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380962559_swigfjjm",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T21:49:22.578Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380962559_swigfjjm",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 1,
  "isDeepThinking": false
}
2025-08-16T21:49:22.578Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380962559_swigfjjm",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:49:22.578Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:49:22.578Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:49:22.578Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380962578_gd64bt9t",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:49:22.579Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380962578_gd64bt9t",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:49:25.031Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755380965014_8thslgn1",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T21:49:25.032Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755380965014_8thslgn1",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:49:25.032Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755380965014_8thslgn1",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:49:25.032Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:49:25.032Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:49:25.032Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755380965032_4ttxry0v",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:49:25.033Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755380965032_4ttxry0v",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:49:30.292Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:49:32.979Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:49:34.174Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:49:38.494Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:49:39.737Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:51:39.855Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:52:36.841Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:52:43.152Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:52:48.323Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 17,
  "userId": 1,
  "messageCount": 2
}
2025-08-16T21:52:57.929Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:53:01.247Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 17,
  "userId": 1
}
2025-08-16T21:53:07.544Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381187528_2m323i41",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T21:53:07.544Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381187528_2m323i41",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:53:07.544Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381187528_2m323i41",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:53:07.545Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:53:07.545Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:53:07.547Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381187547_z3an5ln9",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:53:07.547Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381187547_z3an5ln9",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:53:08.834Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381188818_tbbpg0zw",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T21:53:08.834Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381188818_tbbpg0zw",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:53:08.835Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381188818_tbbpg0zw",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:53:08.835Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:53:08.835Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:53:08.836Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381188836_lvmpubb9",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:53:08.836Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381188836_lvmpubb9",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:53:11.165Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381191150_vmb9frm2",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T21:53:11.165Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381191150_vmb9frm2",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:53:11.165Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381191150_vmb9frm2",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:53:11.165Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:53:11.165Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:53:11.166Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381191166_jx4vneqo",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:53:11.166Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381191166_jx4vneqo",
  "endpoint": "/chat/completions",
  "model": "deepseek-chat",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:58:33.387Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T21:59:01.550Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381541533_mawm49v1",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T21:59:01.551Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381541533_mawm49v1",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 1,
  "isDeepThinking": false
}
2025-08-16T21:59:01.551Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381541533_mawm49v1",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:59:01.551Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:01.552Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:01.553Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381541553_wc1prfis",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:01.554Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381541553_wc1prfis",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:02.986Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381542968_4nigbtrx",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T21:59:02.987Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381542968_4nigbtrx",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:59:02.987Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381542968_4nigbtrx",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:59:02.987Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:02.987Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:02.988Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381542988_x9hg88uv",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:02.988Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381542988_x9hg88uv",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:05.255Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381545240_x8h2qw0f",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T21:59:05.255Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381545240_x8h2qw0f",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:59:05.256Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381545240_x8h2qw0f",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T21:59:05.256Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:05.256Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:05.256Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381545256_k0mj9qyn",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:05.256Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381545256_k0mj9qyn",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:37.379Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381577363_7au5v0ju",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T21:59:37.379Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381577363_7au5v0ju",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:59:37.380Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381577363_7au5v0ju",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T21:59:37.381Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:37.381Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:37.381Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381577381_8f2nx0bc",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:37.381Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381577381_8f2nx0bc",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:38.658Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381578644_sagqvbyb",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 14
}
2025-08-16T21:59:38.659Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381578644_sagqvbyb",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:59:38.659Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381578644_sagqvbyb",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T21:59:38.659Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:38.659Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:38.659Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381578659_y9rlfkm3",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:38.660Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381578659_y9rlfkm3",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:41.002Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381580986_dzwbmqaf",
  "sessionId": 18,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T21:59:41.002Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381580986_dzwbmqaf",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T21:59:41.002Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381580986_dzwbmqaf",
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T21:59:41.002Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 18,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T21:59:41.002Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T21:59:41.003Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381581003_6vjyds5f",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 18,
  "attachmentsCount": 0
}
2025-08-16T21:59:41.003Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381581003_6vjyds5f",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T21:59:59.807Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 18,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:00:04.129Z - DEBUG [session-deleted]: AI chat session deleted successfully
Data: {
  "sessionId": 18,
  "userId": 1
}
2025-08-16T22:00:15.561Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381615546_hajmf9wu",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T22:00:15.562Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381615546_hajmf9wu",
  "messageCount": 1,
  "lastUserMessagePreview": "testtest",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:15.562Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381615546_hajmf9wu",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:00:15.562Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:15.562Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:15.562Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381615562_33ny3gka",
  "messageCount": 2,
  "lastUserMessage": "testtest...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:15.563Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381615562_33ny3gka",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:00:16.819Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381616805_1bgm96xz",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 14
}
2025-08-16T22:00:16.820Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381616805_1bgm96xz",
  "messageCount": 1,
  "lastUserMessagePreview": "testtest",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:16.820Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381616805_1bgm96xz",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:00:16.820Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:16.820Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:16.821Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381616821_ranejgl5",
  "messageCount": 2,
  "lastUserMessage": "testtest...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:16.821Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381616821_ranejgl5",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:00:19.089Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381619071_z48n0jm6",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:00:19.090Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381619071_z48n0jm6",
  "messageCount": 1,
  "lastUserMessagePreview": "testtest",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:19.090Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381619071_z48n0jm6",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:00:19.090Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:19.091Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:19.091Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381619091_4p7ld0br",
  "messageCount": 2,
  "lastUserMessage": "testtest...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:19.092Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381619091_4p7ld0br",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:00:23.495Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381623480_l90ppork",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T22:00:23.495Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381623480_l90ppork",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:23.495Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381623480_l90ppork",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T22:00:23.496Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:23.496Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:23.496Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381623496_y0qfvv3x",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:23.496Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381623496_y0qfvv3x",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:00:24.747Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381624732_imtgbwsc",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T22:00:24.748Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381624732_imtgbwsc",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:24.748Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381624732_imtgbwsc",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T22:00:24.748Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:24.748Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:24.749Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381624749_34one3y9",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:24.749Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381624749_34one3y9",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:00:27.104Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381627087_3bkwhy6h",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T22:00:27.104Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381627087_3bkwhy6h",
  "messageCount": 4,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:00:27.105Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381627087_3bkwhy6h",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 4
}
2025-08-16T22:00:27.105Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:00:27.105Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:00:27.105Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381627105_bz64ipyw",
  "messageCount": 5,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:00:27.106Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381627105_bz64ipyw",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:03.981Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 19,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:03:07.537Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381787519_kvipeydx",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:03:07.538Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381787519_kvipeydx",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:07.538Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381787519_kvipeydx",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:07.538Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:07.539Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:03:07.539Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:07.540Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381787540_z2ho226f",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:03:07.541Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381787540_z2ho226f",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:08.855Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381788837_aj23wtt8",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:03:08.856Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381788837_aj23wtt8",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:08.856Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381788837_aj23wtt8",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:08.856Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:08.856Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:03:08.856Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:08.856Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381788856_mox3bu3e",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:03:08.857Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381788856_mox3bu3e",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:11.110Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381791092_4qv3bbjz",
  "sessionId": 19,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:03:11.110Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381791092_4qv3bbjz",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:11.110Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381791092_4qv3bbjz",
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:11.110Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:11.111Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 19,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free"
}
2025-08-16T22:03:11.111Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:11.111Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381791111_2td1vpaf",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 19,
  "attachmentsCount": 0
}
2025-08-16T22:03:11.111Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381791111_2td1vpaf",
  "endpoint": "/chat/completions",
  "model": "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:27.718Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381807700_4qca0aq6",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:03:27.719Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381807700_4qca0aq6",
  "messageCount": 1,
  "lastUserMessagePreview": "tes",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:27.719Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381807700_4qca0aq6",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:27.719Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:27.719Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:03:27.719Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:27.720Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381807720_3hmv2vj3",
  "messageCount": 2,
  "lastUserMessage": "tes...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:03:27.720Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381807720_3hmv2vj3",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:29.131Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381809110_sunaes5j",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 21
}
2025-08-16T22:03:29.132Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381809110_sunaes5j",
  "messageCount": 1,
  "lastUserMessagePreview": "tes",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:29.143Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381809110_sunaes5j",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:29.144Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:29.144Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:03:29.145Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:29.145Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381809145_oxmecwn8",
  "messageCount": 2,
  "lastUserMessage": "tes...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:03:29.146Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381809145_oxmecwn8",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:03:31.533Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755381811515_qbnkdapf",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:03:31.533Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755381811515_qbnkdapf",
  "messageCount": 1,
  "lastUserMessagePreview": "tes",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:03:31.533Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755381811515_qbnkdapf",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:03:31.534Z - DEBUG [api-key]: Using OpenRouter API key starting with: v1-f412cb9...
Data: {
  "provider": "openrouter"
}
2025-08-16T22:03:31.534Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:03:31.534Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:03:31.534Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755381811534_3ni4myjy",
  "messageCount": 2,
  "lastUserMessage": "tes...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:03:31.534Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755381811534_3ni4myjy",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:10:08.736Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 20,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:10:13.359Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382213344_xl7yfhae",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T22:10:13.360Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382213344_xl7yfhae",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:10:13.360Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382213344_xl7yfhae",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:10:13.361Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:10:13.361Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:10:13.361Z - DEBUG [openrouter-headers]: OpenRouter headers configured
Data: {
  "referer": "https://retroforum.replit.app",
  "title": "RetroForum AI Chat",
  "hasApiKey": true,
  "apiKeyLength": 67
}
2025-08-16T22:10:13.364Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382213363_oeeoce7p",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:10:13.366Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382213363_oeeoce7p",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:10:14.653Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382214637_9omseuu3",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 16
}
2025-08-16T22:10:14.654Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382214637_9omseuu3",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:10:14.654Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382214637_9omseuu3",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:10:14.654Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:10:14.654Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:10:14.655Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382214655_9xbv20mr",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:10:14.656Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382214655_9xbv20mr",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:10:16.941Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382216924_qyl3ojk7",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T22:10:16.941Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382216924_qyl3ojk7",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:10:16.941Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382216924_qyl3ojk7",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:10:16.942Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:10:16.942Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:10:16.942Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382216942_p8zxrlwp",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:10:16.942Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382216942_p8zxrlwp",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:11:07.682Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 20,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:11:17.126Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 20,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:11:19.707Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382279688_gswboysp",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 19
}
2025-08-16T22:11:19.708Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382279688_gswboysp",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:11:19.708Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382279688_gswboysp",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:11:19.709Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:11:19.710Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:11:19.710Z - DEBUG [openrouter-headers]: OpenRouter headers configured
Data: {
  "referer": "https://retroforum.replit.app",
  "title": "RetroForum AI Chat",
  "hasApiKey": true,
  "apiKeyLength": 67
}
2025-08-16T22:11:19.712Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:11:19.712Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382279712_1z2x0kw3",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:11:19.712Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382279712_1z2x0kw3",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:11:20.994Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382280976_vek0p1oz",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:11:20.996Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382280976_vek0p1oz",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:11:20.997Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382280976_vek0p1oz",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:11:21.001Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:11:21.002Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:11:21.003Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:11:21.005Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382281003_qdal3d8g",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:11:21.006Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382281003_qdal3d8g",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:11:23.372Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382283354_mkod2dmc",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 18
}
2025-08-16T22:11:23.373Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382283354_mkod2dmc",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:11:23.373Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382283354_mkod2dmc",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:11:23.373Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:11:23.373Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:11:23.374Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:11:23.374Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382283374_25imiwo9",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:11:23.374Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382283374_25imiwo9",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:12:55.358Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 20,
  "userId": 1,
  "messageCount": 0
}
2025-08-16T22:14:07.877Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382447860_tgjjx3nw",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T22:14:07.877Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382447860_tgjjx3nw",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:14:07.878Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382447860_tgjjx3nw",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:14:07.880Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:14:07.880Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:14:07.881Z - DEBUG [openrouter-headers]: OpenRouter headers configured
Data: {
  "referer": "https://retroforum.replit.app",
  "title": "RetroForum AI Chat",
  "hasApiKey": true,
  "apiKeyLength": 67
}
2025-08-16T22:14:07.883Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:14:07.883Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382447883_e9yloirr",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:14:07.884Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382447883_e9yloirr",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:14:09.191Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382449174_c0430bch",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 17
}
2025-08-16T22:14:09.191Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382449174_c0430bch",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:14:09.191Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382449174_c0430bch",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:14:09.191Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:14:09.192Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:14:09.192Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:14:09.192Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382449192_482h4bam",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:14:09.192Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382449192_482h4bam",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:14:11.492Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1755382451477_rd206w81",
  "sessionId": 20,
  "userId": 1,
  "validationTime": 15
}
2025-08-16T22:14:11.492Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1755382451477_rd206w81",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-16T22:14:11.493Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1755382451477_rd206w81",
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-16T22:14:11.493Z - DEBUG [init]: Initializing AIClient
Data: {
  "userId": 1,
  "sessionId": 20,
  "isDeepThinking": false,
  "hasAttachments": false,
  "stream": true,
  "provider": "openrouter",
  "model": "tngtech/deepseek-r1t2-chimera:free"
}
2025-08-16T22:14:11.493Z - DEBUG [timeout-config]: Setting AI request timeout
Data: {
  "baseTimeout": 30000,
  "configuredTimeout": 30000,
  "finalTimeout": 30000,
  "testMode": false
}
2025-08-16T22:14:11.493Z - DEBUG [openrouter-payload]: OpenRouter request payload
Data: {
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "messageCount": 2,
  "temperature": 0.7,
  "maxTokens": 2000,
  "stream": true
}
2025-08-16T22:14:11.494Z - DEBUG [request-prep]: Preparing AI request
Data: {
  "requestId": "req_1755382451493_2s5eydaq",
  "messageCount": 2,
  "lastUserMessage": "test...",
  "stream": true,
  "isDeepThinking": false,
  "userId": 1,
  "sessionId": 20,
  "attachmentsCount": 0
}
2025-08-16T22:14:11.494Z - DEBUG [api-request]: Sending request to API
Data: {
  "requestId": "req_1755382451493_2s5eydaq",
  "endpoint": "/chat/completions",
  "model": "tngtech/deepseek-r1t2-chimera:free",
  "stream": true,
  "retryCount": 0
}
2025-08-16T22:16:42.906Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 20,
  "userId": 1,
  "messageCount": 0
}
2025-08-31T05:44:02.254Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756619042251_fjxvcefk",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 1,
  "isDeepThinking": false
}
2025-08-31T05:44:02.254Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756619042251_fjxvcefk",
  "userId": 1,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T05:44:03.292Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756619043292_tdig2j6n",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-31T05:44:03.293Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756619043292_tdig2j6n",
  "userId": 1,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T05:44:05.320Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756619045320_18qgofy3",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-31T05:44:05.320Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756619045320_18qgofy3",
  "userId": 1,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T06:00:52.636Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 4,
  "userId": 1,
  "messageCount": 0
}
2025-08-31T06:01:11.331Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1756620071330_qygzuico",
  "sessionId": 4,
  "userId": 1,
  "validationTime": 1
}
2025-08-31T06:01:11.331Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756620071330_qygzuico",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-31T06:01:11.332Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756620071330_qygzuico",
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T06:01:12.357Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1756620072356_jkfsqtyi",
  "sessionId": 4,
  "userId": 1,
  "validationTime": 1
}
2025-08-31T06:01:12.357Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756620072356_jkfsqtyi",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-31T06:01:12.357Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756620072356_jkfsqtyi",
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T06:01:14.373Z - DEBUG [session]: Session validated successfully
Data: {
  "requestId": "req_1756620074373_r51c5jgg",
  "sessionId": 4,
  "userId": 1,
  "validationTime": 0
}
2025-08-31T06:01:14.373Z - DEBUG [preprocessing]: Formatted messages for AI
Data: {
  "requestId": "req_1756620074373_r51c5jgg",
  "messageCount": 1,
  "lastUserMessagePreview": "test",
  "processingTime": 0,
  "isDeepThinking": false
}
2025-08-31T06:01:14.373Z - DEBUG [stream-start]: Starting streaming response
Data: {
  "requestId": "req_1756620074373_r51c5jgg",
  "userId": 1,
  "sessionId": 4,
  "isDeepThinking": false,
  "messageCount": 1
}
2025-08-31T06:28:36.901Z - DEBUG [session-messages-retrieved]: Session messages retrieved
Data: {
  "sessionId": 4,
  "userId": 1,
  "messageCount": 0
}
