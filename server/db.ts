import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from "@shared/schema";
import fs from 'fs';
import path from 'path';

// Database configuration - using SQLite for compatibility
const DATABASE_PATH = process.env.DATABASE_PATH || './retro_forum.db';
const DATABASE_DIR = path.dirname(DATABASE_PATH);

// Ensure database directory exists
if (!fs.existsSync(DATABASE_DIR)) {
  fs.mkdirSync(DATABASE_DIR, { recursive: true });
}

// Create SQLite connection (DuckDB-compatible for local dev)
let sqlite: Database.Database;

try {
  sqlite = new Database(DATABASE_PATH);
  // Enable WAL mode for better concurrency and MCP access
  sqlite.pragma('journal_mode = WAL');
  sqlite.pragma('foreign_keys = ON');
  console.log(`SQLite database connected at: ${DATABASE_PATH}`);
} catch (error) {
  console.error('Failed to initialize SQLite database:', error);
  throw error;
}

// For backward compatibility with existing session store code
export const pool = {
  query: async (text: string, params?: any[]): Promise<{ rows: any[] }> => {
    try {
      const stmt = sqlite.prepare(text);
      const result = params ? stmt.all(...params) : stmt.all();
      return { rows: result };
    } catch (error) {
      throw error;
    }
  },
  connect: async () => {
    // Return a client-like object for compatibility
    return {
      query: pool.query,
      release: () => {} // No-op for SQLite
    };
  },
  end: async () => {
    return new Promise<void>((resolve) => {
      if (sqlite) {
        sqlite.close();
        resolve();
      } else {
        resolve();
      }
    });
  },
  on: (event: string, handler: Function) => {
    // No-op for compatibility, SQLite doesn't use the same event system
    if (event === 'connect') {
      // Simulate connection event
      setTimeout(() => handler(), 100);
    }
  }
};

// Custom reconnection function
export const reconnectIfNeeded = async () => {
  try {
    // Test connection with a simple query
    await pool.query('SELECT 1');
    return true;
  } catch (error) {
    console.error('Database connection failed, attempting to reconnect:', error);
    try {
      // For SQLite, we just need to recreate the database connection
      if (sqlite) {
        sqlite.close();
      }
      sqlite = new Database(DATABASE_PATH);
      sqlite.pragma('journal_mode = WAL');
      sqlite.pragma('foreign_keys = ON');
      console.log('SQLite reconnected successfully');
      return true;
    } catch (reconnectError) {
      console.error('Failed to reconnect to database:', reconnectError);
      return false;
    }
  }
};

export const db = drizzle(sqlite, { schema });

// Export a function to safely execute database operations with reconnection attempts
export async function executeWithRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
  let lastError;
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      const isConnectionError = 
        error.message.includes('terminating connection') || 
        error.message.includes('Connection terminated') ||
        error.message.includes('Connection refused');
      
      if (isConnectionError && attempt < maxRetries - 1) {
        console.log(`Database operation failed with connection error: ${error.message}. Attempt ${attempt + 1}/${maxRetries}. Reconnecting...`);
        await reconnectIfNeeded();
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
      } else {
        throw error;
      }
    }
  }
  throw lastError;
}
